{"skeleton": {"hash": "dc5qsqQs7e300vPbjYYNK94n/0g", "spine": "3.7.94", "width": 713, "height": 73.57, "images": "./images/", "audio": "E:/Ktek/2020/Baccarat_New/Animation/notify_Fake_Anim"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "<PERSON>ame", "parent": "bone", "scaleY": 0.768}, {"name": "effect", "parent": "bone", "scaleX": 6.016, "scaleY": 1.202, "shearY": -31.74}], "slots": [{"name": "Frame01", "bone": "<PERSON>ame", "attachment": "Frame01"}, {"name": "effect", "bone": "effect", "color": "fff98fff", "attachment": "effect", "blend": "additive"}, {"name": "WIN", "bone": "root"}], "skins": {"default": {"Frame01": {"Frame01": {"width": 713, "height": 72}, "Frame02": {"width": 154, "height": 50}}, "effect": {"effect": {"x": 0.5, "width": 45, "height": 72}}}}, "animations": {"toast_dai": {"slots": {"Frame01": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.565, 0.09, 0.187, 0.99]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [0.565, 0.09, 0.187, 0.99]}, {"time": 1.8333, "color": "ffffff00"}]}, "effect": {"color": [{"time": 0.1667, "color": "fff88f00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "color": "fff78fff", "curve": "stepped"}, {"time": 0.6, "color": "fff78fff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "color": "fff88f00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.1667, "name": "effect"}, {"time": 1, "name": null}]}}, "bones": {"Frame": {"translate": [{"time": 0, "x": -273.92, "y": 0, "curve": [0.565, 0.09, 0.187, 0.99]}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": [0.565, 0.09, 0.187, 0.99]}, {"time": 1.8333, "x": 66.35, "y": 0}]}, "effect": {"translate": [{"time": 0.1667, "x": 60.5, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -229.11, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0.773}]}}}, "toast_ngan": {"slots": {"Frame01": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.565, 0.09, 0.187, 0.99]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Frame02"}]}, "effect": {"color": [{"time": 0, "color": "fff88f00", "curve": "stepped"}, {"time": 0.2667, "color": "fff88f00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "color": "fff78fff", "curve": "stepped"}, {"time": 0.5667, "color": "fff78fff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "color": "fff88f00"}], "attachment": [{"time": 1, "name": null}]}}, "bones": {"Frame": {"translate": [{"time": 0, "x": -273.92, "y": 0, "curve": [0.565, 0.09, 0.187, 0.99]}, {"time": 0.6667, "x": 0, "y": 0}]}, "effect": {"translate": [{"time": 0, "x": 59.44, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 34.41, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -71.73, "y": 0}], "scale": [{"time": 0, "x": 0.56, "y": 0.517}]}}}}}