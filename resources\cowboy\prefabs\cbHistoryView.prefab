[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "cb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 12}, {"__id__": 17}, {"__id__": 35}], "_active": true, "_level": 2, "_components": [{"__id__": 70}, {"__id__": 71}], "_prefab": {"__id__": 72}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 4, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "c6jnBvuFNN96Tw4YnpA2Z/", "sync": false}, {"__type__": "cc.Node", "_name": "nen popup copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 7}], "_active": true, "_level": 3, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 950, "height": 612}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-9, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "title_chonDong", "_objFlags": 0, "_parent": {"__id__": 6}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 8}], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 172, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.8, 249.4, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "652146c1-8700-4854-b999-fee847df1580"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "0fCC1pxRFEarIvySGPig6X", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "bd1c6ad2-c75e-4e7b-8ed1-8a7758010bca"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "0fWADY9bpF2qX8c967+4wO", "sync": false}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 4, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 83, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [443.6, 242.3, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "27b33a13-3522-4e78-bfb0-1f445235b84c"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "duration": 0.1, "zoomScale": 0.9, "clickEvents": [{"__id__": 15}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 12}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "c890fFIrOZAObefUnoFLaiq", "handler": "backClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "27NJiHzo5EHoie0wRndIXO", "sync": false}, {"__type__": "cc.Node", "_name": "spriteBGTitle", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 18}, {"__id__": 21}, {"__id__": 24}, {"__id__": 27}, {"__id__": 30}], "_active": true, "_level": 2, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5, 170, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lbSessionID", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 19}], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-318, -1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_useOriginalSize": false, "_string": "PHIÊN", "_N$string": "PHIÊN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "d9itxtVmFOcqfJFORRyYV/", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-148, -1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "_useOriginalSize": false, "_string": "THỜI GIAN", "_N$string": "THỜI GIAN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "e4I1L/QXNAs5UJmulTbTc6", "sync": false}, {"__type__": "cc.Node", "_name": "lbBet", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [22, -1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "_useOriginalSize": false, "_string": "CƯỢC", "_N$string": "CƯỢC", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "47K7OUO0lIl6bhXQqKdoAQ", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 28}], "_prefab": {"__id__": 29}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [165, -1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "_useOriginalSize": false, "_string": "THẮNG", "_N$string": "THẮNG", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "7axeC1fMVHl7KAkvsjENsG", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 17}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 31}], "_prefab": {"__id__": 32}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [319, -1, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_useOriginalSize": false, "_string": "CHI TIẾT", "_N$string": "CHI TIẾT", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "1eWpTz7YZGv69Cllip6CLY", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f8511a04-a9d9-4975-afa5-e3283e2bdaad"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "2cXqrYAkZNV4RG/bPID05n", "sync": false}, {"__type__": "cc.Node", "_name": "bg<PERSON><PERSON>nt", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 36}], "_active": true, "_level": 2, "_components": [], "_prefab": {"__id__": 69}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-24, -9, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 35}, "_children": [{"__id__": 37}, {"__id__": 59}], "_active": true, "_level": 3, "_components": [{"__id__": 65}, {"__id__": 66}, {"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 804, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [20, -25, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [{"__id__": 38}], "_active": true, "_level": 5, "_components": [], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 37}, "_children": [{"__id__": 39}, {"__id__": 42}, {"__id__": 45}, {"__id__": 48}, {"__id__": 51}], "_active": true, "_level": 5, "_components": [{"__id__": 56}], "_prefab": {"__id__": 57}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 780, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lbSessionID", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-318, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "_useOriginalSize": false, "_string": "#66553620", "_N$string": "#66553620", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "2dFlliM0xH5JD15NSAcc7q", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-148, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "_useOriginalSize": false, "_string": "17:03 23-03-2019", "_N$string": "17:03 23-03-2019", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "7fTOunVslLwqAdNcKCZ82X", "sync": false}, {"__type__": "cc.Node", "_name": "lbBet", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 46}], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [22, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_useOriginalSize": false, "_string": "250.000", "_N$string": "250.000", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "4aZKGGCD5IXonW2wsd/BNw", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 49}], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "_useOriginalSize": false, "_string": "50.000.000", "_N$string": "50.000.000", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "3abt3t0GVBQLPGy3Fg/Aah", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 52}, {"__id__": 53}], "_prefab": {"__id__": 55}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 219, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 10, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [319, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "_useOriginalSize": false, "_string": "<PERSON>em chi tiết", "_N$string": "<PERSON>em chi tiết", "_fontSize": 20, "_lineHeight": 48, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 54}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 51}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 38}, "component": "", "_componentId": "98b467lg1tGSIOSS7iaiI4s", "handler": "openDetailClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "fbB6yOqUtCcoa38D1e+lEc", "sync": false}, {"__type__": "98b467lg1tGSIOSS7iaiI4s", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "lbSessionID": {"__id__": 40}, "lbTime": {"__id__": 43}, "lbBet": {"__id__": 46}, "lbWin": {"__id__": 49}, "jackpotColor": {"__type__": "cc.Color", "r": 255, "g": 219, "b": 0, "a": 255}, "bigWinColor": {"__type__": "cc.Color", "r": 0, "g": 230, "b": 253, "a": 255}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "beJegR6bxLHJtBQZypQhNr", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "17rZcmypJElbDhu8sMZNGA", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [{"__id__": 60}], "_active": true, "_level": 0, "_components": [{"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 803, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [], "_active": true, "_level": 0, "_components": [{"__id__": 61}], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 803, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 182, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "d0QHbTB4lPwYfAabko8NgJ", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "c7K/0qF/lHwIhA4rfTxTjJ", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 60}, "content": {"__id__": 60}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "94f0dcGWSRC9ovLsmjATU3F", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "itemTemplate": {"__id__": 38}, "scrollView": {"__id__": 65}, "spawnCount": 15, "spacing": 10, "bufferZone": 400, "_id": ""}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "771/3CHz9C3Knd3ggWgEPw", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "acEr/7v65LYKSkuwbdI8ec", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, "_clips": [{"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, {"__uuid__": "2c581fef-58f4-478d-adee-4138a71c7df4"}], "playOnLoad": false, "_id": ""}, {"__type__": "c890fFIrOZAObefUnoFLaiq", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "slotsHistoryListView": {"__id__": 66}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "12189a4e-5d8e-455d-8f5b-da9b0b78f939"}, "fileId": "e8ZviA26FPfJDALwqCeJHs", "sync": false}]